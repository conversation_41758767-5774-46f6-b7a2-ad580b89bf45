import { renderHook, act } from '@testing-library/react';
import {
  useConcurrentNavigation,
  useDeferredSearch,
  useConcurrentForm,
  useConcurrentData,
  useConcurrentAnimation,
} from '../useConcurrentFeatures';
import { waitForConcurrentUpdate, waitForTransition } from '../../test/setup';

describe('useConcurrentNavigation', () => {
  it('should handle navigation with transitions', async () => {
    const { result } = renderHook(() => useConcurrentNavigation());

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.navigateWithTransition(() => {
        // Mock navigation action
      });
    });

    expect(result.current.isPending).toBe(true);

    await waitForTransition();

    expect(result.current.isPending).toBe(false);
  });
});

describe('useDeferredSearch', () => {
  it('should defer value updates', async () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDeferredSearch(value),
      { initialProps: { value: 'initial' } }
    );

    expect(result.current.deferredValue).toBe('initial');
    expect(result.current.isStale).toBe(false);

    // Update the value
    rerender({ value: 'updated' });

    // Initially, the deferred value should still be the old value
    expect(result.current.deferredValue).toBe('initial');
    expect(result.current.isStale).toBe(true);

    // After concurrent update, deferred value should update
    await waitForConcurrentUpdate();

    expect(result.current.deferredValue).toBe('updated');
    expect(result.current.isStale).toBe(false);
  });
});

describe('useConcurrentForm', () => {
  it('should handle form updates with transitions', async () => {
    const { result } = renderHook(() => useConcurrentForm());
    const mockUpdateFn = jest.fn();

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.updateFormWithTransition(mockUpdateFn);
    });

    expect(result.current.isPending).toBe(true);
    expect(mockUpdateFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isPending).toBe(false);
  });
});

describe('useConcurrentData', () => {
  it('should handle data with deferred values and refresh', async () => {
    const mockRefreshFn = jest.fn();
    const { result, rerender } = renderHook(
      ({ data, isLoading }) => useConcurrentData(data, isLoading),
      { initialProps: { data: 'initial', isLoading: false } }
    );

    expect(result.current.data).toBe('initial');
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isStale).toBe(false);

    // Update data
    rerender({ data: 'updated', isLoading: false });

    // Should be stale initially
    expect(result.current.isStale).toBe(true);

    // Test refresh functionality
    act(() => {
      result.current.refreshData(mockRefreshFn);
    });

    expect(result.current.isLoading).toBe(true);
    expect(mockRefreshFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isLoading).toBe(false);
  });
});

describe('useConcurrentAnimation', () => {
  it('should handle animations with transitions', async () => {
    const { result } = renderHook(() => useConcurrentAnimation());
    const mockAnimationFn = jest.fn();

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.animateWithTransition(mockAnimationFn);
    });

    expect(result.current.isPending).toBe(true);
    expect(mockAnimationFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isPending).toBe(false);
  });
});

describe('Concurrent Features Integration', () => {
  it('should work together without conflicts', async () => {
    const { result: navResult } = renderHook(() => useConcurrentNavigation());
    const { result: formResult } = renderHook(() => useConcurrentForm());
    const { result: animResult } = renderHook(() => useConcurrentAnimation());

    // All should start as not pending
    expect(navResult.current.isPending).toBe(false);
    expect(formResult.current.isPending).toBe(false);
    expect(animResult.current.isPending).toBe(false);

    // Trigger multiple concurrent operations
    act(() => {
      navResult.current.navigateWithTransition(() => {});
      formResult.current.updateFormWithTransition(() => {});
      animResult.current.animateWithTransition(() => {});
    });

    // All should be pending
    expect(navResult.current.isPending).toBe(true);
    expect(formResult.current.isPending).toBe(true);
    expect(animResult.current.isPending).toBe(true);

    await waitForTransition();

    // All should complete
    expect(navResult.current.isPending).toBe(false);
    expect(formResult.current.isPending).toBe(false);
    expect(animResult.current.isPending).toBe(false);
  });
});
